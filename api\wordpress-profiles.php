<?php
/**
 * WordPress Profiles API
 * Handles CRUD operations for WordPress domain profiles
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/config.php';
require_once '../classes/WordPressProfileService.php';

try {
    $profileService = new WordPressProfileService();
    $method = $_SERVER['REQUEST_METHOD'];
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    // Check for JSON decode errors
    if ($rawInput && $input === null && json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid JSON: ' . json_last_error_msg()
        ]);
        exit;
    }

    switch ($method) {
        case 'GET':
            handleGetRequest($profileService);
            break;

        case 'POST':
            handlePostRequest($profileService, $input);
            break;

        case 'PUT':
            handlePutRequest($profileService, $input);
            break;

        case 'DELETE':
            handleDeleteRequest($profileService, $input);
            break;

        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}

function handleGetRequest($profileService) {
    if (isset($_GET['action'])) {
        switch ($_GET['action']) {
            case 'list':
                $activeOnly = isset($_GET['active_only']) && $_GET['active_only'] === 'true';
                $profiles = $profileService->getAllProfiles($activeOnly);
                echo json_encode([
                    'success' => true,
                    'profiles' => $profiles
                ]);
                break;
                
            case 'get':
                if (!isset($_GET['id'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'error' => 'Profile ID required']);
                    return;
                }
                
                $profile = $profileService->getProfile((int)$_GET['id']);
                if ($profile) {
                    echo json_encode([
                        'success' => true,
                        'profile' => $profile
                    ]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'error' => 'Profile not found']);
                }
                break;
                
            case 'statistics':
                $stats = $profileService->getPostingStatistics();
                echo json_encode([
                    'success' => true,
                    'statistics' => $stats
                ]);
                break;
                
            case 'test_connection':
                if (!isset($_GET['id'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'error' => 'Profile ID required']);
                    return;
                }
                
                $result = $profileService->testProfileConnection((int)$_GET['id']);
                echo json_encode($result);
                break;
                
            case 'novel_profiles':
                if (!isset($_GET['novel_id'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'error' => 'Novel ID required']);
                    return;
                }
                
                $profiles = $profileService->getNovelPostingProfiles((int)$_GET['novel_id']);
                echo json_encode([
                    'success' => true,
                    'profiles' => $profiles
                ]);
                break;
                
            case 'chapter_profiles':
                if (!isset($_GET['chapter_id'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'error' => 'Chapter ID required']);
                    return;
                }
                
                $profiles = $profileService->getChapterPostingProfiles((int)$_GET['chapter_id']);
                echo json_encode([
                    'success' => true,
                    'profiles' => $profiles
                ]);
                break;
                
            default:
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Invalid action']);
                break;
        }
    } else {
        // Default: return all active profiles
        $profiles = $profileService->getAllProfiles(true);
        echo json_encode([
            'success' => true,
            'profiles' => $profiles
        ]);
    }
}

function handlePostRequest($profileService, $input) {
    if (!isset($input['action'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Action required']);
        return;
    }
    
    switch ($input['action']) {
        case 'create':
            if (!isset($input['profile_data'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Profile data required']);
                return;
            }
            
            $result = $profileService->createProfile($input['profile_data']);
            if ($result['success']) {
                http_response_code(201);
            } else {
                http_response_code(400);
            }
            echo json_encode($result);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
            break;
    }
}

function handlePutRequest($profileService, $input) {
    if (!isset($input['action']) || $input['action'] !== 'update') {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Update action required']);
        return;
    }

    if (!isset($input['profile_id']) || !isset($input['profile_data'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Profile ID and data required']);
        return;
    }

    $result = $profileService->updateProfile((int)$input['profile_id'], $input['profile_data']);
    if (!$result['success']) {
        http_response_code(400);
    }
    echo json_encode($result);
}

function handleDeleteRequest($profileService, $input) {
    if (!isset($input['action']) || $input['action'] !== 'delete') {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Delete action required']);
        return;
    }

    if (!isset($input['profile_id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Profile ID required']);
        return;
    }

    $result = $profileService->deleteProfile((int)$input['profile_id']);
    if (!$result['success']) {
        http_response_code(400);
    }
    echo json_encode($result);
}
